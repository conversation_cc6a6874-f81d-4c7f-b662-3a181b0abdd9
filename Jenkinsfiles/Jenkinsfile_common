/////////////////////////////////////
	// INSTALL BY USER : COMPOSER INSTALL - BUILD APP
	/////////////////////////////////////
   def stageInstall(String currentDate, String path, String targetEnvironment, String appName) {
	   stage('Install') {
		   GIT_COMMIT_HASH = sh (script: "git log -n 1 --pretty=format:'(%h) %cd'", returnStdout: true)

		   // Initialize Git submodules for microservices that use space-mongo-documents
		   def mongoDocumentServices = ['space-proc-shop', 'space-proc-me']
		   if (mongoDocumentServices.contains(appName)) {
		       echo "Initializing Git submodules for ${appName}"
		       sh "git submodule update --init --recursive"
		   }

		   // Use different docker-compose file based on the microservice name
		   if (appName == 'spaceProcDataApi') {
			    sh "rm -rf ./.git"
		            sh "<NAME_EMAIL>:D4UDigitalPlatform/space-aws-infra.git /tmp/${currentDate}"
		            sh "cp -r  /tmp/${currentDate}/config/_init/docker/docker-compose_build_nodejs_app_image.yml ${path}/"
			    sh "cp -r  /tmp/${currentDate}/config/_init/docker/aws/nodejs_app_image/* ${path}/"
		            sh "cp -r /tmp/${currentDate}/deployment/scripts/* ${path}/"
		            sh "rm -rf /tmp/${currentDate}"
		            sh "bash ${path}/container.sh ${currentDate}"
		            sh "bash ${path}/aws_login_docker.sh"
		            // If the microservice is 'spaceProcDataApi', use the specific docker-compose file for Node.js
			    sh "CONTAINER_NAME=${currentDate} /usr/local/bin/docker-compose --project-name=${currentDate} --file ${path}/docker-compose_build_nodejs_app_image.yml build --no-cache"
			    sh "CONTAINER_NAME=${currentDate} /usr/local/bin/docker-compose --project-name=${currentDate} --file ${path}/docker-compose_build_nodejs_app_image.yml up -d"
			//	sh "docker exec ${currentDate} bash -c 'ls -R'"
			// sh "docker exec ${currentDate} bash -c 'bash /var/www/tmp/spacemiddleware_nodejs_install.sh ${targetEnvironment} ${appName} --gitcommit \"${GIT_COMMIT_HASH}\"'"
		   } else {
		       sh "rm -rf ./.git"
		       sh "<NAME_EMAIL>:D4UDigitalPlatform/space-aws-infra.git /tmp/${currentDate}"
		       sh "cp -r  /tmp/${currentDate}/config/_init ${path}"
		       sh "cp -r /tmp/${currentDate}/deployment/scripts ${path}"
		       sh "rm -rf /tmp/${currentDate}"
		       sh "bash ${path}/scripts/container.sh ${currentDate}"
		       sh "bash ${path}/scripts/aws_login_docker.sh"
		       // Default docker-compose file for other microservices
		       sh "CONTAINER_NAME=${currentDate} /usr/local/bin/docker-compose --project-name=${currentDate} --file ${path}/_init/docker/docker-compose_build_app_image.yml build --no-cache"
		       sh "CONTAINER_NAME=${currentDate} /usr/local/bin/docker-compose --project-name=${currentDate} --file ${path}/_init/docker/docker-compose_build_app_image.yml up -d"
		       sh "docker exec ${currentDate} bash -c 'bash /var/www/tmp/scripts/spacemiddleware_install.sh ${targetEnvironment} ${appName} --gitcommit \"${GIT_COMMIT_HASH}\"'"
		   }
	   }
	}

   ////////////////////////////////////
   // PUSH   IN ECR (ECS SERVICE AWS)
   ///////////////////////////////////
   def stagePushImage(String currentDate, String targetEnvironment, String appName, String path) {
	   stage('Push') {
		if (appName == 'spaceProcDataApi') {
			sh "bash ${path}/spacemiddleware_push.sh ${env.BRANCH_NAME} ${targetEnvironment} ${currentDate} ${appName}"
		} else {
			sh "bash ${path}/scripts/spacemiddleware_push.sh ${env.BRANCH_NAME} ${targetEnvironment} ${currentDate} ${appName}"
	}
	}
   }

   ////////////////////////////////////
   // DEPLOY IMAGE
   ///////////////////////////////////
   def stageDeploy(String currentDate, String targetEnvironment, String appName, String path) {
	   stage('Deploy') {
		if (appName == 'spaceProcDataApi') {
			sh "bash ${path}/deploy/space_deploy.sh ${env.BRANCH_NAME} ${targetEnvironment} ${currentDate} ${appName}"
		} else {
			sh "bash ${path}/scripts/deploy/space_deploy.sh ${env.BRANCH_NAME} ${targetEnvironment} ${currentDate} ${appName}"
	   }
	}
	}


	/////////////////////////////////////
	// CREATE SYMBOLIC LINKS
	/////////////////////////////////////
   def stageSymbolicLinks(String currentDate, String projectName, String targetEnvironment, String appName) {
	   stage('Symbolic links') {
         sh "docker exec ${currentDate} bash -c 'bash /var/www/tmp/scripts/space_symbolic-links.sh ${targetEnvironment} ${appName}'"
	   }
	}

	////////////////////////////////////

   return this
