pipeline {
    agent any
    stages {
        stage('Build') {
            steps {
                echo 'Starting build ms service .'
            }
        }

        stage('space-exp-shop') {
            when { changeset "*/space-exp-shop/**"}
            steps {
                echo "====space-exp-shop===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-exp-shop']], wait: false, propagate: false
            }
        }

        stage('space-exp-me') {
            when { changeset "*/space-exp-me/**"}
            steps {
                echo "====space-exp-me===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-exp-me']], wait: false, propagate: false
            }
        }

        stage('space-proc-user-veh') {
            when { changeset "*/space-proc-user-veh/**"}
            steps {
                echo "====space-proc-user-veh===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-proc-user-veh']], wait: false, propagate: false
            }
        }

        stage('space-proc-me') {
            when {
                anyOf {
                    changeset "*/space-proc-me/**"
                    changeset "space-mongo-documents/**"
                }
            }
            steps {
                echo "====space-proc-me===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-proc-me']], wait: false, propagate: false
            }
        }


        stage('space-proc-shop') {
            when {
                anyOf {
                    changeset "*/space-proc-shop/**"
                    changeset "space-mongo-documents/**"
                }
            }
            steps {
                echo "====space-proc-shop===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-proc-shop']], wait: false, propagate: false
            }
        }

        stage('space-proc-settings') {
            when { changeset "*/space-proc-settings/**"}
            steps {
                echo "====space-proc-settings===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-proc-settings']], wait: false, propagate: false
            }
        }

        stage('space-proc-car') {
            when { changeset "*/space-proc-car/**"}
            steps {
                echo "====space-proc-car===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-proc-car']], wait: false, propagate: false
            }
        }

        stage('space-proc-notification') {
            when { changeset "*/space-proc-notification/**"}
            steps {
                echo "====space-proc-notification===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-proc-notification']], wait: false, propagate: false
            }
        }

        stage('space-proc-charging-station') {
            when { changeset "*/space-proc-charging-station/**"}
            steps {
                echo "====space-proc-charging-station===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-proc-charging-station']], wait: false, propagate: false
            }
        }

        stage('space-proc-data-api') {
            when { changeset "*/space-proc-data-api/**"}
            steps {
                echo "====space-proc-data-api===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-proc-data-api']], wait: false, propagate: false
            }
        }

        stage('space-proc-log-incident') {
            when { changeset "*/space-proc-log-incident/**"}
            steps {
                echo "====space-proc-log-incident===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-proc-log-incident']], wait: false, propagate: false
            }
        }

        stage('space-mongo-documents-changes') {
            when { changeset "space-mongo-documents/**"}
            steps {
                echo "====space-mongo-documents changes detected===="
                echo "This will trigger builds for dependent microservices: space-proc-shop, space-proc-me"
                // Note: Dependent microservices will be triggered automatically by their individual stages
            }
        }

        stage('space-sys-idp') {
            when { changeset "*/space-sys-idp/**"}
            steps {
                echo "====space-sys-idp===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-sys-idp']], wait: false, propagate: false
            }
        }

        stage('space-sys-mop') {
            when { changeset "*/space-sys-mop/**"}
            steps {
                echo "====space-sys-mop===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-sys-mop']], wait: false, propagate: false
            }
        }

        stage('space-sys-notification') {
            when { changeset "*/space-sys-notification/**"}
            steps {
                echo "====space-sys-notification===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-sys-notification']], wait: false, propagate: false
            }
        }
        stage('space-sys-omni') {
            when { changeset "*/space-sys-omni/**"}
            steps {
                echo "====space-sys-omni===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-sys-omni']], wait: false, propagate: false
            }
        }

        stage('space-sys-sdpr') {
            when { changeset "*/space-sys-sdpr/**"}
            steps {
                echo "====space-sys-sdpr===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-sys-sdpr']], wait: false, propagate: false
            }
        }

        stage('space-sys-apdv') {
            when { changeset "*/space-sys-apdv/**"}
            steps {
                echo "====space-sys-apdv===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-sys-apdv']], wait: false, propagate: false
            }
        }

        stage('space-sys-firebase') {
            when { changeset "*/space-sys-firebase/**"}
            steps {
                echo "====space-sys-firebase===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-sys-firebase']], wait: false, propagate: false
            }
        }

        stage('space-sys-service-advisor') {
            when { changeset "*/space-sys-service-advisor/**"}
            steps {
                echo "====space-sys-service-advisor===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-sys-service-advisor']], wait: false, propagate: false
            }
        }

        stage('space-sys-f2m-locator') {
            when { changeset "*/space-sys-f2m-locator/**"}
            steps {
                echo "====space-sys-f2m-locator===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-sys-f2m-locator']], wait: false, propagate: false
            }
        }

        stage('space-sys-f2mc') {
            when { changeset "*/space-sys-f2mc/**"}
            steps {
                echo "====space-sys-f2mc===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-sys-f2mc']], wait: false, propagate: false
            }
        }

        stage('space-sys-sams-data') {
            when { changeset "*/space-sys-sams-data/**"}
            steps {
                echo "====space-sys-sams-data===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-sys-sams-data']], wait: false, propagate: false
            }
        }
        stage('space-sys-gadp') {
            when { changeset "*/space-sys-gadp/**"}
            steps {
                echo "====space-sys-gadp===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-sys-gadp']], wait: false, propagate: false
            }
        }
        stage('space-sys-user-db') {
            when { changeset "*/space-sys-user-db/**"}
            steps {
                echo "====space-sys-user-db===="
                build job: '/APP/Space_microservice/Space_Microservice_Develop/develop', parameters: [[$class: 'StringParameterValue', name: 'appName', value: 'space-sys-user-db']], wait: false, propagate: false
            }
        }
    }
}
