# Space MongoDB Documents Jenkins Integration

## Overview
This document outlines the Jenkins configuration changes made to support the space-mongo-documents shared library integration with space-proc-shop and other dependent microservices.

## Changes Made

### 1. Jenkinsfile_common Updates
**File**: `Jenkins<PERSON>les/Jenkinsfile_common`

- **Added verification step** for space-mongo-documents availability
- **Added logging** to verify the space-mongo-documents directory exists during build
- **Updated comments** to document space-mongo-documents support
- **Microservices affected**: space-proc-shop, space-proc-me

**Key Changes**:
```groovy
// Verify space-mongo-documents availability for microservices that use it
def mongoDocumentServices = ['space-proc-shop', 'space-proc-me']
if (mongoDocumentServices.contains(appName)) {
    echo "Verifying space-mongo-documents availability for ${appName}"
    sh "ls -la space-mongo-documents/ || echo 'Warning: space-mongo-documents directory not found'"
}
```

### 2. Jenkinsfile_auto Updates
**File**: `<PERSON><PERSON><PERSON>/Jenkins<PERSON>le_auto`

- **Updated space-proc-shop stage** to trigger on space-mongo-documents changes
- **Updated space-proc-me stage** to trigger on space-mongo-documents changes
- **Added dedicated stage** for space-mongo-documents change detection

**Key Changes**:
```groovy
stage('space-proc-shop') {
    when {
        anyOf {
            changeset "*/space-proc-shop/**"
            changeset "space-mongo-documents/**"
        }
    }
    // ... build steps
}

stage('space-proc-me') {
    when {
        anyOf {
            changeset "*/space-proc-me/**"
            changeset "space-mongo-documents/**"
        }
    }
    // ... build steps
}

stage('space-mongo-documents-changes') {
    when { changeset "space-mongo-documents/**"}
    steps {
        echo "====space-mongo-documents changes detected===="
        echo "This will trigger builds for dependent microservices: space-proc-shop, space-proc-me"
    }
}
```

## How It Works

### Build Triggers
1. **Direct microservice changes**: When files in `process/space-proc-shop/` or `process/space-proc-me/` are modified
2. **Shared library changes**: When files in `space-mongo-documents/` are modified
3. **Automatic dependency handling**: Changes to space-mongo-documents automatically trigger builds for dependent microservices

### Build Process
1. Jenkins detects changes in space-mongo-documents or dependent microservices
2. The build process verifies space-mongo-documents directory availability
3. Composer installs the space-mongo-documents dependency using the local path repository
4. Docker containers are built with the updated dependencies
5. Deployment proceeds with the updated shared library

## Microservices Using space-mongo-documents

### Currently Integrated:
- **space-proc-shop**: Uses MongoDB ODM with space-mongo-documents
- **space-proc-me**: Uses MongoDB ODM with space-mongo-documents

### Configuration Details:
Both microservices have:
- `composer.json` dependency: `"space/mongo-documents": "*"`
- Repository path: `"url": "../../space-mongo-documents"`
- Doctrine ODM mapping for Space\MongoDocuments namespace

## Benefits

1. **Automatic builds**: Changes to shared MongoDB documents trigger builds for dependent services
2. **Consistency**: Ensures all dependent microservices use the same version of shared documents
3. **Visibility**: Clear logging shows when space-mongo-documents is being used
4. **Maintainability**: Centralized shared library reduces code duplication

## Future Considerations

- Add more microservices to the `mongoDocumentServices` array as they adopt space-mongo-documents
- Consider adding validation steps to ensure space-mongo-documents compatibility
- Monitor build performance impact of additional change detection

## Notes

- space-mongo-documents is maintained as a separate folder, not a Git submodule
- The shared library uses Composer's path repository feature for local development
- All existing business logic and API response formats are preserved
