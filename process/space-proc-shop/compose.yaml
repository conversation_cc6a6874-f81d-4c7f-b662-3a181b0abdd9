
services:
###> doctrine/mongodb-odm-bundle ###
  mongodb:
    # In production, you may want to use a managed database service
    image: mongodb/mongodb-atlas-local:latest
    environment:
      - MONGODB_INITDB_DATABASE=${MONGODB_DB:-app}
      # You should definitely set a root username and password in production
      - MONGODB_INITDB_ROOT_USERNAME=${MONGODB_USERNAME:-}
      - MONGODB_INITDB_ROOT_PASSWORD=${MONGODB_PASSWORD:-}
    volumes:
      - mongodb_data:/data/db:rw
      # You may use a bind-mounted host directory instead, so that it is harder to accidentally remove the volume and lose all your data!
      # - ./docker/mongodb/data:/data/db:rw
###< doctrine/mongodb-odm-bundle ###

volumes:
###> doctrine/mongodb-odm-bundle ###
  mongodb_data:
###< doctrine/mongodb-odm-bundle ###
