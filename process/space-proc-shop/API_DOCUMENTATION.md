# Space Proc Shop API Documentation

## Overview
This document provides detailed information about the space-proc-shop service APIs, including usage examples, request/response formats, and recommendations for improvement.

## Base URL
```
http://localhost:8000
```

## Common Headers
Some endpoints require the following headers:
- `userId`: User identification (e.g., "6f5853d0f9e2442c9c818fecf0eec7cf")
- `vin`: Vehicle identification number (e.g., "VR3UPHNKSKT101603")

## Endpoints

### 1. Catalog API
#### GET /v1/catalog
Retrieves catalog data for a specific brand, country, and language.

**Request Parameters:**
- Headers:
  - `userId` (required): User identification
  - `vin` (required): Vehicle identification number
- Query Parameters:
  - `brand` (required): Brand code (e.g., "DS")
  - `country` (required): Country code (e.g., "IT")
  - `language` (required): Language code (e.g., "it")

**Example Request:**
```bash
curl -X GET "http://localhost:8000/v1/catalog?brand=DS&country=IT&language=it" \
     -H "userId: 6f5853d0f9e2442c9c818fecf0eec7cf" \
     -H "vin: VR3UPHNKSKT101603"
```

**Recommendations:**
- Document valid brand codes in API documentation
- Provide better error messages with valid options
- Add response caching for frequently accessed catalog data
- Implement versioning for catalog updates

### 2. Dealers List API
#### GET /v1/dealers-list
Retrieves a list of dealers based on location and brand.

**Request Parameters:**
- Query Parameters:
  - `brand` (required): Brand code (e.g., "DS")
  - `country` (required): Country code (e.g., "IT")
  - `language` (required): Language code (e.g., "it")
  - `latitude` (required): Latitude coordinate (e.g., 45.46796)
  - `longitude` (required): Longitude coordinate (e.g., 9.18178)

**Example Request:**
```bash
curl -X GET "http://localhost:8000/v1/dealers-list?brand=DS&country=IT&language=it&latitude=45.46796&longitude=9.18178"
```

**Response Example:**
```json
{
  "dealers": [
    {
      "name": "DS STORE MILANO",
      "address": {
        "address1": "VIA GATTAMELATA, 41",
        "city": "MILANO",
        "zip_code": "20149"
      },
      "distance": 3.1,
      "coordinates": {
        "latitude": 45.485151,
        "longitude": 9.150323
      },
      "phones": {
        "PhoneNumber": "**********"
      }
    }
  ]
}
```

**Recommendations:**
- Implement pagination for large result sets
- Add filtering by dealer services
- Add sorting options (by distance, rating, etc.)
- Cache frequently requested locations

### 3. EV Charging Station API
#### GET /v1/ev_routing/charging-station-availability
Retrieves charging station availability information.

**Request Parameters:**
- Query Parameters:
  - `brand` (required): Brand code (e.g., "DS")
  - `latitude` (required): Latitude coordinate
  - `longitude` (required): Longitude coordinate
  - `chargingAvailability` (required): Boolean flag

**Example Request:**
```bash
curl -X GET "http://localhost:8000/v1/ev_routing/charging-station-availability?brand=DS&latitude=45.46796&longitude=9.18178&chargingAvailability=true"
```

**Response Example:**
```json
{
  "success": {
    "chargingStationsAvailability": []
  }
}
```

**Recommendations:**
- Add test data for development environment
- Implement detailed error messages for invalid coordinates
- Add filtering by charging station type
- Include real-time availability updates
- Add documentation about data refresh rates

### 4. PHYD (Pay How You Drive) API
#### GET /v1/vehicle/driving-score
Retrieves driving score and related metrics.

**Request Parameters:**
- Headers:
  - `userId` (required): User identification
  - `vin` (required): Vehicle identification number

**Example Request:**
```bash
curl -X GET "http://localhost:8000/v1/vehicle/driving-score" \
     -H "userId: 6f5853d0f9e2442c9c818fecf0eec7cf" \
     -H "vin: VR3UPHNKSKT101603"
```

**Response Example:**
```json
{
  "success": {
    "data": {
      "overallScore": {
        "value": 36.28
      },
      "dailyScore": {
        "value": 47.73,
        "subScore": {
          "dynamics": {
            "percentageOfGood": 47.99,
            "percentageOfAverage": 77.35,
            "percentageOfBad": 45.75
          }
        }
      },
      "featureCodeStatus": "enable"
    }
  }
}
```

**Recommendations:**
- Add date range filters for historical data
- Implement score trend analysis
- Add documentation for score calculation methodology
- Include comparison with average scores
- Add export functionality for reports

### 5. Subscription API
#### GET /v1/subscription
Retrieves subscription information for a user's vehicle.

**Request Parameters:**
- Headers:
  - `userId` (required): User identification
  - `vin` (required): Vehicle identification number

**Example Request:**
```bash
curl -X GET "http://localhost:8000/v1/subscription" \
     -H "userId: 6f5853d0f9e2442c9c818fecf0eec7cf" \
     -H "vin: VR3UPHNKSKT101603"
```

**Current Status:** Not Implemented

**Recommendations:**
- Implement the endpoint
- Add proper route configuration
- Include subscription status checks
- Add subscription management capabilities
- Implement notification system for subscription events

## General Recommendations

### Security
- Implement rate limiting
- Add API key authentication
- Use HTTPS for all endpoints
- Implement request validation
- Add request logging

### Performance
- Implement caching strategies
- Add response compression
- Optimize database queries
- Implement connection pooling
- Monitor API performance

### Documentation
- Add OpenAPI/Swagger documentation
- Include request/response examples
- Document error codes and messages
- Add integration test examples
- Maintain changelog

### Monitoring
- Add health check endpoints
- Implement logging for all endpoints
- Add performance monitoring
- Track error rates
- Monitor resource usage

## Error Handling
All endpoints should return standardized error responses:

```json
{
  "error": {
    "message": "Error description",
    "code": "ERROR_CODE",
    "details": {}
  }
}
```

Common HTTP status codes:
- 200: Success
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 500: Internal Server Error

## Rate Limiting
To prevent abuse, the API implements rate limiting:
- 100 requests per minute per IP
- 1000 requests per hour per API key

## Support
For API support, contact:
- Email: <EMAIL>
- Documentation: https://docs.example.com
- API Status: https://status.example.com 